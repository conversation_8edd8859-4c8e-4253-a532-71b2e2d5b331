import React, { useState, useRef } from 'react';
import { Tabs } from 'antd';
import { YTHForm } from 'yth-ui';
import type { ActionType } from 'yth-ui/es/components/form/listFilter';
import type { Form } from '@formily/core/esm/models';

const { TabPane } = Tabs;

// 定义学习对象类型
interface DicDataType {
  code: string;
  text: string;
}

const defaultQueryData: { learningObject: DicDataType[] } = {
  learningObject: [
    { code: 'A08A39A01', text: '主要负责人' },
    { code: 'A08A39A02', text: '安全管理人员' },
    { code: 'A08A39A03', text: '员工' },
    { code: 'A08A39A04', text: '承包商' },
  ],
};

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    learningObject?: DicDataType[];
  };
};

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC<PropsTypes> = ({
  // onComplete, // 完成回调，暂未使用
  // closeModal, // 关闭弹框方法，暂未使用
  defaultQuery = {},
}) => {
  // 当前激活的tab key
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  // 获取learningObject数据，优先使用传入的defaultQuery，否则使用默认数据
  const learningObjectData: DicDataType[] =
    defaultQuery?.learningObject || defaultQueryData.learningObject;

  // 设置默认激活的tab（第一个选项）
  React.useEffect(() => {
    if (learningObjectData && learningObjectData.length > 0 && !activeTabKey) {
      setActiveTabKey(learningObjectData[0].code);
    }
  }, [learningObjectData, activeTabKey]);

  // tab切换处理函数
  const handleTabChange: (key: string) => void = (key: string) => {
    setActiveTabKey(key);
  };

  // 为每个tab创建独立的form和actionRef
  const formRefs = useRef<Record<string, Form>>({});
  const actionRefs = useRef<Record<string, React.MutableRefObject<ActionType & {
    delRow: (name: string, index: number) => void;
    addRows: (name: string, data: object[]) => void;
    copyRow: (name: string, index: number) => void;
  } | undefined>>>({});

  // 获取或创建指定tab的form
  const getFormForTab = (tabKey: string): Form => {
    if (!formRefs.current[tabKey]) {
      formRefs.current[tabKey] = YTHForm.createForm({});
    }
    return formRefs.current[tabKey];
  };

  // 获取或创建指定tab的actionRef
  const getActionRefForTab = (tabKey: string) => {
    if (!actionRefs.current[tabKey]) {
      actionRefs.current[tabKey] = useRef();
    }
    return actionRefs.current[tabKey];
  };

  // 根据不同tab获取对应的列配置
  const getColumnsForTab = (tabKey: string) => {
    const baseColumns = [
      {
        title: '姓名',
        name: 'name',
        minWidth: 120,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
      {
        title: '工号',
        name: 'employeeId',
        minWidth: 120,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
      {
        title: '部门',
        name: 'department',
        minWidth: 150,
        edit: true,
        componentName: 'Input' as const,
        componentProps: {},
        required: true,
      },
    ];

    // 根据不同的tab添加特定的列
    switch (tabKey) {
      case 'A08A39A01': // 主要负责人
        return [
          ...baseColumns,
          {
            title: '职务',
            name: 'position',
            minWidth: 120,
            edit: true,
            componentName: 'Input' as const,
            componentProps: {},
            required: true,
          },
          {
            title: '联系电话',
            name: 'phone',
            minWidth: 150,
            edit: true,
            componentName: 'Input' as const,
            componentProps: {},
            required: true,
          },
        ];
      case 'A08A39A02': // 安全管理人员
        return [
          ...baseColumns,
          {
            title: '安全证书编号',
            name: 'certificateNo',
            minWidth: 150,
            edit: true,
            componentName: 'Input' as const,
            componentProps: {},
            required: true,
          },
          {
            title: '证书有效期',
            name: 'certificateExpiry',
            minWidth: 120,
            edit: true,
            componentName: 'DatePicker' as const,
            componentProps: {},
            required: true,
          },
        ];
      case 'A08A39A03': // 员工
        return [
          ...baseColumns,
          {
            title: '岗位',
            name: 'jobPosition',
            minWidth: 120,
            edit: true,
            componentName: 'Input' as const,
            componentProps: {},
            required: true,
          },
          {
            title: '入职时间',
            name: 'hireDate',
            minWidth: 120,
            edit: true,
            componentName: 'DatePicker' as const,
            componentProps: {},
            required: false,
          },
        ];
      case 'A08A39A04': // 承包商
        return [
          ...baseColumns,
          {
            title: '承包商名称',
            name: 'contractorName',
            minWidth: 200,
            edit: true,
            componentName: 'Input' as const,
            componentProps: {},
            required: true,
          },
          {
            title: '合同编号',
            name: 'contractNo',
            minWidth: 150,
            edit: true,
            componentName: 'Input' as const,
            componentProps: {},
            required: true,
          },
        ];
      default:
        return baseColumns;
    }
  };

  // 渲染tab内容
  const renderTabContent: (tabKey: string) => React.ReactNode = (tabKey: string) => {
    const currentTab: DicDataType | undefined = learningObjectData.find(
      (item: DicDataType) => item.code === tabKey,
    );
    const form = getFormForTab(tabKey);
    const actionRef = getActionRefForTab(tabKey);

    return (
      <div style={{ padding: '20px' }}>
        <h3 style={{ marginBottom: '20px' }}>{currentTab?.text}管理</h3>
        <YTHForm form={form}>
          <YTHForm.List
            title={`${currentTab?.text}列表`}
            columns={getColumnsForTab(tabKey)}
            name="personnelList"
            rowOperations={[
              {
                key: `delete_${tabKey}_${Date.now()}`,
                title: '删除',
                type: 'danger',
                operation: (index, row: { id?: string }) => {
                  if (row && row.id) {
                    // 如果有ID，可以在这里调用删除API
                    console.log('删除人员:', row);
                  }
                  actionRef.current?.delRow('personnelList', index + 1);
                },
              },
              {
                key: `copy_${tabKey}_${Date.now()}`,
                title: '复制',
                type: 'primary',
                operation: (index) => {
                  actionRef.current?.copyRow('personnelList', index + 1);
                },
              },
            ]}
            extra={[
              {
                key: 'addPersonnel',
                title: `添加${currentTab?.text}`,
                type: 'main',
                operation: () => {
                  actionRef.current?.addRows('personnelList', [{}]);
                },
              },
            ]}
            actionRef={actionRef}
          />
        </YTHForm>
      </div>
    );
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
        {learningObjectData.map((item: DicDataType) => (
          <TabPane tab={item.text} key={item.code}>
            {renderTabContent(item.code)}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default AssessmentPersonnelList;
