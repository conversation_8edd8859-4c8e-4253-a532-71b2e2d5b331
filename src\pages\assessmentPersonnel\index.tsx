import React, { useState } from 'react';
import { Tabs } from 'antd';

const { TabPane } = Tabs;

// 定义学习对象类型
interface DicDataType {
  code: string;
  text: string;
}

const defaultQueryData: { learningObject: DicDataType[] } = {
  learningObject: [
    { code: 'A08A39A01', text: '主要负责人' },
    { code: 'A08A39A02', text: '安全管理人员' },
    { code: 'A08A39A03', text: '员工' },
    { code: 'A08A39A04', text: '承包商' },
  ],
};

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    learningObject?: DicDataType[];
  };
};

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC<PropsTypes> = ({
  // onComplete, // 完成回调，暂未使用
  // closeModal, // 关闭弹框方法，暂未使用
  defaultQuery = {},
}) => {
  // 当前激活的tab key
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  // 获取learningObject数据，优先使用传入的defaultQuery，否则使用默认数据
  const learningObjectData: DicDataType[] =
    defaultQuery?.learningObject || defaultQueryData.learningObject;

  // 设置默认激活的tab（第一个选项）
  React.useEffect(() => {
    if (learningObjectData && learningObjectData.length > 0 && !activeTabKey) {
      setActiveTabKey(learningObjectData[0].code);
    }
  }, [learningObjectData, activeTabKey]);

  // tab切换处理函数
  const handleTabChange: (key: string) => void = (key: string) => {
    setActiveTabKey(key);
  };

  // 渲染tab内容
  const renderTabContent: (tabKey: string) => React.ReactNode = (tabKey: string) => {
    const currentTab: DicDataType | undefined = learningObjectData.find(
      (item: DicDataType) => item.code === tabKey,
    );
    return (
      <div style={{ padding: '20px' }}>
        <h3>当前选中: {currentTab?.text}</h3>
        <p>Tab Key: {tabKey}</p>
        {/* 这里可以根据不同的tab渲染不同的内容 */}
        <div>
          {/* 根据tabKey渲染对应的业务组件 */}
          {tabKey === 'A08A39A01' && <div>主要负责人相关内容</div>}
          {tabKey === 'A08A39A02' && <div>安全管理人员相关内容</div>}
          {tabKey === 'A08A39A03' && <div>员工相关内容</div>}
          {tabKey === 'A08A39A04' && <div>承包商相关内容</div>}
        </div>
      </div>
    );
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Tabs activeKey={activeTabKey} onChange={handleTabChange}>
        {learningObjectData.map((item: DicDataType) => (
          <TabPane tab={item.text} key={item.code}>
            {renderTabContent(item.code)}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default AssessmentPersonnelList;
